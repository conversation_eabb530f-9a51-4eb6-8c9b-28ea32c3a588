'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { classroomSchema, type ClassroomFormData } from '@/lib/validations';
import { type School, type Classroom } from '@/lib/db';
import { Plus, Edit, Trash2, Building } from 'lucide-react';

interface ClassroomWithSchool extends Classroom {
  school_name: string;
}

export default function ClassroomsPage() {
  const [classrooms, setClassrooms] = useState<ClassroomWithSchool[]>([]);
  const [schools, setSchools] = useState<School[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingClassroom, setEditingClassroom] = useState<ClassroomWithSchool | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<ClassroomFormData>({
    resolver: zodResolver(classroomSchema),
  });

  const selectedSchoolId = watch('school_id');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      const [classroomsRes, schoolsRes] = await Promise.all([
        fetch('/api/classrooms'),
        fetch('/api/schools'),
      ]);

      if (classroomsRes.ok && schoolsRes.ok) {
        const [classroomsData, schoolsData] = await Promise.all([
          classroomsRes.json(),
          schoolsRes.json(),
        ]);
        setClassrooms(classroomsData);
        setSchools(schoolsData);
      } else {
        setError('Errore nel caricamento dei dati');
      }
    } catch (error) {
      setError('Errore di connessione');
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: ClassroomFormData) => {
    setIsSubmitting(true);
    setError('');

    try {
      const url = editingClassroom ? `/api/classrooms/${editingClassroom.id}` : '/api/classrooms';
      const method = editingClassroom ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        await loadData();
        setIsDialogOpen(false);
        reset();
        setEditingClassroom(null);
      } else {
        const result = await response.json();
        setError(result.error || 'Errore durante il salvataggio');
      }
    } catch (error) {
      setError('Errore di connessione');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (classroom: ClassroomWithSchool) => {
    setEditingClassroom(classroom);
    reset({
      room_number: classroom.room_number,
      section: classroom.section,
      school_id: classroom.school_id,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (classroom: ClassroomWithSchool) => {
    if (!confirm(`Sei sicuro di voler eliminare l'aula "${classroom.room_number}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/classrooms/${classroom.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await loadData();
      } else {
        const result = await response.json();
        setError(result.error || 'Errore durante l\'eliminazione');
      }
    } catch (error) {
      setError('Errore di connessione');
    }
  };

  const handleNewClassroom = () => {
    setEditingClassroom(null);
    reset({ room_number: '', section: '', school_id: 0 });
    setIsDialogOpen(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Caricamento aule...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-poppins font-bold text-foreground">Gestione Aule</h1>
          <p className="text-muted-foreground mt-2">
            Gestisci le aule delle scuole registrate
          </p>
        </div>
        <Button 
          onClick={handleNewClassroom} 
          className="bg-secondary hover:bg-secondary/90"
          disabled={schools.length === 0}
        >
          <Plus className="h-4 w-4 mr-2" />
          Aggiungi Aula
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
          {error}
        </div>
      )}

      {/* No Schools Warning */}
      {schools.length === 0 && (
        <Card className="border-warning/20 bg-warning/5">
          <CardContent className="pt-6">
            <div className="text-center">
              <Building className="h-12 w-12 text-warning mx-auto mb-4" />
              <p className="text-warning font-medium">Nessuna scuola disponibile</p>
              <p className="text-muted-foreground text-sm mt-1">
                Devi prima registrare almeno una scuola per poter creare delle aule.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Classrooms Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Building className="h-5 w-5 mr-2" />
            Aule ({classrooms.length})
          </CardTitle>
          <CardDescription>
            Elenco di tutte le aule registrate nel sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          {classrooms.length === 0 ? (
            <div className="text-center py-8">
              <Building className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Nessuna aula registrata</p>
              {schools.length > 0 && (
                <Button onClick={handleNewClassroom} variant="outline" className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  Aggiungi la prima aula
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Numero Aula</TableHead>
                  <TableHead>Sezione</TableHead>
                  <TableHead>Scuola</TableHead>
                  <TableHead>Data Creazione</TableHead>
                  <TableHead className="text-right">Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {classrooms.map((classroom) => (
                  <TableRow key={classroom.id}>
                    <TableCell className="font-medium">{classroom.room_number}</TableCell>
                    <TableCell>{classroom.section}</TableCell>
                    <TableCell>{classroom.school_name}</TableCell>
                    <TableCell>
                      {new Date(classroom.created_at).toLocaleDateString('it-IT')}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(classroom)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(classroom)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingClassroom ? 'Modifica Aula' : 'Aggiungi Nuova Aula'}
            </DialogTitle>
            <DialogDescription>
              {editingClassroom 
                ? 'Modifica i dettagli dell\'aula selezionata'
                : 'Inserisci i dettagli della nuova aula'
              }
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="room_number">Numero Aula</Label>
              <Input
                id="room_number"
                placeholder="es. A101, Lab1, Palestra"
                {...register('room_number')}
              />
              {errors.room_number && (
                <p className="text-sm text-destructive">{errors.room_number.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="section">Sezione</Label>
              <Input
                id="section"
                placeholder="es. A, B, Scienze, Informatica"
                {...register('section')}
              />
              {errors.section && (
                <p className="text-sm text-destructive">{errors.section.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="school_id">Scuola</Label>
              <Select
                value={selectedSchoolId?.toString() || ''}
                onValueChange={(value) => setValue('school_id', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleziona una scuola" />
                </SelectTrigger>
                <SelectContent>
                  {schools.map((school) => (
                    <SelectItem key={school.id} value={school.id.toString()}>
                      {school.name} - {school.city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.school_id && (
                <p className="text-sm text-destructive">{errors.school_id.message}</p>
              )}
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
              >
                Annulla
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-secondary hover:bg-secondary/90"
              >
                {isSubmitting ? 'Salvataggio...' : (editingClassroom ? 'Aggiorna' : 'Salva')}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
