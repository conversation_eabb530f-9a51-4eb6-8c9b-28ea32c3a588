import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { classroomSchema } from '@/lib/validations';
import { requireAuth } from '@/lib/auth';

// GET /api/classrooms - List all classrooms
export async function GET() {
  try {
    await requireAuth();
    
    const classrooms = await db.getClassrooms();
    return NextResponse.json(classrooms);
    
  } catch (error) {
    console.error('Error fetching classrooms:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    );
  }
}

// POST /api/classrooms - Create a new classroom
export async function POST(request: NextRequest) {
  try {
    await requireAuth();
    
    const body = await request.json();
    
    // Validate input
    const validatedData = classroomSchema.parse(body);
    
    // Create classroom
    const classroom = await db.createClassroom(
      validatedData.room_number,
      validatedData.section,
      validatedData.school_id
    );
    
    return NextResponse.json(classroom, { status: 201 });
    
  } catch (error) {
    console.error('Error creating classroom:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Dati non validi' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore durante la creazione dell\'aula' },
      { status: 500 }
    );
  }
}
