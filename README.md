# Sistema di Gestione Scolastica

Un sistema completo per la gestione di scuole, aule e insegnanti costruito con Next.js, TypeScript e Neon PostgreSQL.

## Caratteristiche

- **Gestione Scuole**: Crea, modifica ed elimina scuole con nome e città
- **Gestione Aule**: Gestisci aule con numero, sezione e associazione alla scuola
- **Gestione Insegnanti**: Registra insegnanti con informazioni complete (nome, email, telefono, materia, giorno libero)
- **Autenticazione Sicura**: Sistema di login amministratore con JWT e bcrypt
- **Design Responsivo**: Interfaccia ottimizzata per desktop e mobile
- **Interfaccia Italiana**: Tutte le etichette e messaggi in italiano

## Tecnologie Utilizzate

- **Frontend**: Next.js 15 con TypeScript
- **Database**: Neon PostgreSQL
- **Styling**: Tailwind CSS v4 con shadcn/ui
- **Autenticazione**: JWT con bcrypt per l'hashing delle password
- **Validazione**: Zod per la validazione dei form
- **Fonts**: Google Fonts (Poppins per i titoli, Roboto per il testo)

## Installazione

1. Clona il repository:
```bash
git clone <repository-url>
cd scuola-manager
```

2. Installa le dipendenze:
```bash
npm install
```

3. Configura le variabili d'ambiente:
```bash
cp .env.example .env.local
```

Modifica `.env.local` con le tue credenziali del database Neon.

4. Esegui lo schema del database:
Il database è già configurato con le tabelle necessarie. Lo schema si trova in `schema.sql`.

5. Avvia il server di sviluppo:
```bash
npm run dev
```

6. Apri [http://localhost:3000](http://localhost:3000) nel browser.

## Credenziali di Default

- **Username**: admin
- **Password**: admin123

⚠️ **Importante**: Cambia queste credenziali in produzione!

## Struttura del Database

### Tabelle Principali

- **schools**: Gestione delle scuole (id, name, city)
- **classrooms**: Gestione delle aule (id, room_number, section, school_id)
- **teachers**: Gestione degli insegnanti (id, first_name, last_name, email, phone, subject, free_day)
- **admins**: Autenticazione amministratori (id, username, password_hash)

### Relazioni

- Le aule sono collegate alle scuole tramite foreign key
- Trigger automatici per l'aggiornamento dei timestamp
- Indici per ottimizzare le performance

## API Endpoints

### Autenticazione
- `POST /api/auth/login` - Login amministratore
- `POST /api/auth/logout` - Logout
- `GET /api/auth/verify` - Verifica sessione

### Scuole
- `GET /api/schools` - Lista scuole
- `POST /api/schools` - Crea scuola
- `GET /api/schools/[id]` - Dettagli scuola
- `PUT /api/schools/[id]` - Aggiorna scuola
- `DELETE /api/schools/[id]` - Elimina scuola

### Aule
- `GET /api/classrooms` - Lista aule
- `POST /api/classrooms` - Crea aula
- `GET /api/classrooms/[id]` - Dettagli aula
- `PUT /api/classrooms/[id]` - Aggiorna aula
- `DELETE /api/classrooms/[id]` - Elimina aula

### Insegnanti
- `GET /api/teachers` - Lista insegnanti
- `POST /api/teachers` - Crea insegnante
- `GET /api/teachers/[id]` - Dettagli insegnante
- `PUT /api/teachers/[id]` - Aggiorna insegnante
- `DELETE /api/teachers/[id]` - Elimina insegnante

## Palette Colori

- **Background Primario**: #F7F9FB (bianco morbido)
- **Accento Primario**: #2E5AAC (blu professionale)
- **Accento Secondario**: #4CAF50 (verde fresco)
- **Successo**: #81C784 (verde chiaro)
- **Avviso**: #FFB300 (giallo caldo)
- **Errore**: #E53935 (rosso elegante)
- **Testo Primario**: #1C1C1C (quasi nero)
- **Testo Secondario**: #5F6368 (grigio neutro)

## Sicurezza

- Password hashate con bcrypt (12 rounds)
- Token JWT con scadenza 24h
- Cookie HTTP-only per i token
- Middleware per la protezione delle route
- Validazione input con Zod
- Protezione CSRF

## Sviluppo

```bash
# Avvia in modalità sviluppo
npm run dev

# Build per produzione
npm run build

# Avvia in produzione
npm start

# Linting
npm run lint
```

## Licenza

Questo progetto è rilasciato sotto licenza MIT.
