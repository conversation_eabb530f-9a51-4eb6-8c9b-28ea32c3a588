@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-roboto);
  --font-heading: var(--font-poppins);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  /* Custom School Management Color Palette */
  --background: #F7F9FB; /* Soft white background */
  --foreground: #1C1C1C; /* Near black primary text */
  --card: #FFFFFF;
  --card-foreground: #1C1C1C;
  --popover: #FFFFFF;
  --popover-foreground: #1C1C1C;
  --primary: #2E5AAC; /* Professional blue for headers/main buttons */
  --primary-foreground: #FFFFFF;
  --secondary: #4CAF50; /* Fresh green for secondary elements */
  --secondary-foreground: #FFFFFF;
  --muted: #F7F9FB;
  --muted-foreground: #5F6368; /* Neutral gray secondary text */
  --accent: #4CAF50;
  --accent-foreground: #FFFFFF;
  --destructive: #E53935; /* Elegant red for errors */
  --destructive-foreground: #FFFFFF;
  --success: #81C784; /* Light green for success notifications */
  --success-foreground: #1C1C1C;
  --warning: #FFB300; /* Warm yellow for warnings */
  --warning-foreground: #1C1C1C;
  --border: #E0E0E0;
  --input: #FFFFFF;
  --ring: #2E5AAC;
  --chart-1: #2E5AAC;
  --chart-2: #4CAF50;
  --chart-3: #81C784;
  --chart-4: #FFB300;
  --chart-5: #E53935;
  --sidebar: #FFFFFF;
  --sidebar-foreground: #1C1C1C;
  --sidebar-primary: #2E5AAC;
  --sidebar-primary-foreground: #FFFFFF;
  --sidebar-accent: #4CAF50;
  --sidebar-accent-foreground: #FFFFFF;
  --sidebar-border: #E0E0E0;
  --sidebar-ring: #2E5AAC;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    font-family: var(--font-roboto), sans-serif;
  }
  h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-poppins), sans-serif;
  }
}

/* Custom utility classes */
@layer utilities {
  .font-poppins {
    font-family: var(--font-poppins), sans-serif;
  }
  .font-roboto {
    font-family: var(--font-roboto), sans-serif;
  }
}
