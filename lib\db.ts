import { neon } from '@neondatabase/serverless';

if (!process.env.DATABASE_URL) {
  throw new Error('DATABASE_URL is not defined');
}

export const sql = neon(process.env.DATABASE_URL);

// Database types
export interface School {
  id: number;
  name: string;
  city: string;
  created_at: string;
  updated_at: string;
}

export interface Classroom {
  id: number;
  room_number: string;
  section: string;
  school_id: number;
  created_at: string;
  updated_at: string;
  school?: School; // For joined queries
}

export interface Teacher {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  subject: string;
  free_day: 'Lunedì' | 'Martedì' | 'Mercoledì' | 'Giovedì' | 'Venerdì' | 'Sabato' | 'Domenica';
  created_at: string;
  updated_at: string;
}

export interface Admin {
  id: number;
  username: string;
  password_hash: string;
  created_at: string;
  updated_at: string;
}

// Database operations
export const db = {
  // Schools
  async getSchools(): Promise<School[]> {
    return await sql`SELECT * FROM schools ORDER BY name ASC`;
  },

  async getSchoolById(id: number): Promise<School | null> {
    const result = await sql`SELECT * FROM schools WHERE id = ${id}`;
    return result[0] || null;
  },

  async createSchool(name: string, city: string): Promise<School> {
    const result = await sql`
      INSERT INTO schools (name, city) 
      VALUES (${name}, ${city}) 
      RETURNING *
    `;
    return result[0];
  },

  async updateSchool(id: number, name: string, city: string): Promise<School | null> {
    const result = await sql`
      UPDATE schools 
      SET name = ${name}, city = ${city} 
      WHERE id = ${id} 
      RETURNING *
    `;
    return result[0] || null;
  },

  async deleteSchool(id: number): Promise<boolean> {
    const result = await sql`DELETE FROM schools WHERE id = ${id}`;
    return result.length > 0;
  },

  // Classrooms
  async getClassrooms(): Promise<(Classroom & { school_name: string })[]> {
    return await sql`
      SELECT c.*, s.name as school_name 
      FROM classrooms c 
      JOIN schools s ON c.school_id = s.id 
      ORDER BY s.name ASC, c.room_number ASC
    `;
  },

  async getClassroomById(id: number): Promise<Classroom | null> {
    const result = await sql`SELECT * FROM classrooms WHERE id = ${id}`;
    return result[0] || null;
  },

  async createClassroom(room_number: string, section: string, school_id: number): Promise<Classroom> {
    const result = await sql`
      INSERT INTO classrooms (room_number, section, school_id) 
      VALUES (${room_number}, ${section}, ${school_id}) 
      RETURNING *
    `;
    return result[0];
  },

  async updateClassroom(id: number, room_number: string, section: string, school_id: number): Promise<Classroom | null> {
    const result = await sql`
      UPDATE classrooms 
      SET room_number = ${room_number}, section = ${section}, school_id = ${school_id} 
      WHERE id = ${id} 
      RETURNING *
    `;
    return result[0] || null;
  },

  async deleteClassroom(id: number): Promise<boolean> {
    const result = await sql`DELETE FROM classrooms WHERE id = ${id}`;
    return result.length > 0;
  },

  // Teachers
  async getTeachers(): Promise<Teacher[]> {
    return await sql`SELECT * FROM teachers ORDER BY last_name ASC, first_name ASC`;
  },

  async getTeacherById(id: number): Promise<Teacher | null> {
    const result = await sql`SELECT * FROM teachers WHERE id = ${id}`;
    return result[0] || null;
  },

  async createTeacher(
    first_name: string, 
    last_name: string, 
    email: string, 
    phone: string | null, 
    subject: string, 
    free_day: Teacher['free_day']
  ): Promise<Teacher> {
    const result = await sql`
      INSERT INTO teachers (first_name, last_name, email, phone, subject, free_day) 
      VALUES (${first_name}, ${last_name}, ${email}, ${phone}, ${subject}, ${free_day}) 
      RETURNING *
    `;
    return result[0];
  },

  async updateTeacher(
    id: number,
    first_name: string, 
    last_name: string, 
    email: string, 
    phone: string | null, 
    subject: string, 
    free_day: Teacher['free_day']
  ): Promise<Teacher | null> {
    const result = await sql`
      UPDATE teachers 
      SET first_name = ${first_name}, last_name = ${last_name}, email = ${email}, 
          phone = ${phone}, subject = ${subject}, free_day = ${free_day} 
      WHERE id = ${id} 
      RETURNING *
    `;
    return result[0] || null;
  },

  async deleteTeacher(id: number): Promise<boolean> {
    const result = await sql`DELETE FROM teachers WHERE id = ${id}`;
    return result.length > 0;
  },

  // Admin authentication
  async getAdminByUsername(username: string): Promise<Admin | null> {
    const result = await sql`SELECT * FROM admins WHERE username = ${username}`;
    return result[0] || null;
  }
};
