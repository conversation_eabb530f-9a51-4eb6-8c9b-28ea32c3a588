import { NextResponse } from 'next/server';
import { removeAuth<PERSON>ookie } from '@/lib/auth';

export async function POST() {
  try {
    await removeAuthCookie();
    
    return NextResponse.json({
      success: true,
      message: 'Logout effettuato con successo',
    });
    
  } catch (error) {
    console.error('Logout error:', error);
    
    return NextResponse.json(
      { error: 'Errore durante il logout' },
      { status: 500 }
    );
  }
}
