import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { schoolSchema } from '@/lib/validations';
import { requireAuth } from '@/lib/auth';

// GET /api/schools/[id] - Get a single school
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAuth();
    
    const { id } = await params;
    const schoolId = parseInt(id);
    
    if (isNaN(schoolId)) {
      return NextResponse.json(
        { error: 'ID scuola non valido' },
        { status: 400 }
      );
    }
    
    const school = await db.getSchoolById(schoolId);
    
    if (!school) {
      return NextResponse.json(
        { error: 'Scuola non trovata' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(school);
    
  } catch (error) {
    console.error('Error fetching school:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    );
  }
}

// PUT /api/schools/[id] - Update a school
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAuth();
    
    const { id } = await params;
    const schoolId = parseInt(id);
    
    if (isNaN(schoolId)) {
      return NextResponse.json(
        { error: 'ID scuola non valido' },
        { status: 400 }
      );
    }
    
    const body = await request.json();
    
    // Validate input
    const validatedData = schoolSchema.parse(body);
    
    // Update school
    const school = await db.updateSchool(schoolId, validatedData.name, validatedData.city);
    
    if (!school) {
      return NextResponse.json(
        { error: 'Scuola non trovata' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(school);
    
  } catch (error) {
    console.error('Error updating school:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Dati non validi' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore durante l\'aggiornamento della scuola' },
      { status: 500 }
    );
  }
}

// DELETE /api/schools/[id] - Delete a school
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAuth();
    
    const { id } = await params;
    const schoolId = parseInt(id);
    
    if (isNaN(schoolId)) {
      return NextResponse.json(
        { error: 'ID scuola non valido' },
        { status: 400 }
      );
    }
    
    const success = await db.deleteSchool(schoolId);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Scuola non trovata' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ message: 'Scuola eliminata con successo' });
    
  } catch (error) {
    console.error('Error deleting school:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore durante l\'eliminazione della scuola' },
      { status: 500 }
    );
  }
}
