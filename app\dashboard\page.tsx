'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { School, Building, Users, Plus } from 'lucide-react';

interface Stats {
  schools: number;
  classrooms: number;
  teachers: number;
}

export default function DashboardPage() {
  const [stats, setStats] = useState<Stats>({ schools: 0, classrooms: 0, teachers: 0 });
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadStats();
  }, []);

  const loadStats = async () => {
    try {
      const [schoolsRes, classroomsRes, teachersRes] = await Promise.all([
        fetch('/api/schools'),
        fetch('/api/classrooms'),
        fetch('/api/teachers'),
      ]);

      const [schools, classrooms, teachers] = await Promise.all([
        schoolsRes.json(),
        classroomsRes.json(),
        teachersRes.json(),
      ]);

      setStats({
        schools: schools.length || 0,
        classrooms: classrooms.length || 0,
        teachers: teachers.length || 0,
      });
    } catch (error) {
      console.error('Error loading stats:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const statCards = [
    {
      title: 'Scuole',
      value: stats.schools,
      description: 'Scuole registrate nel sistema',
      icon: School,
      href: '/dashboard/schools',
      color: 'text-primary',
      bgColor: 'bg-primary/10',
    },
    {
      title: 'Aule',
      value: stats.classrooms,
      description: 'Aule disponibili',
      icon: Building,
      href: '/dashboard/classrooms',
      color: 'text-secondary',
      bgColor: 'bg-secondary/10',
    },
    {
      title: 'Insegnanti',
      value: stats.teachers,
      description: 'Insegnanti registrati',
      icon: Users,
      href: '/dashboard/teachers',
      color: 'text-accent',
      bgColor: 'bg-accent/10',
    },
  ];

  const quickActions = [
    {
      title: 'Aggiungi Scuola',
      description: 'Registra una nuova scuola nel sistema',
      href: '/dashboard/schools?action=create',
      icon: School,
      color: 'bg-primary hover:bg-primary/90',
    },
    {
      title: 'Aggiungi Aula',
      description: 'Crea una nuova aula per una scuola',
      href: '/dashboard/classrooms?action=create',
      icon: Building,
      color: 'bg-secondary hover:bg-secondary/90',
    },
    {
      title: 'Aggiungi Insegnante',
      description: 'Registra un nuovo insegnante',
      href: '/dashboard/teachers?action=create',
      icon: Users,
      color: 'bg-accent hover:bg-accent/90',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-poppins font-bold text-foreground">Dashboard</h1>
        <p className="text-muted-foreground mt-2">
          Panoramica del sistema di gestione scolastica
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {statCards.map((card) => (
          <Card key={card.title} className="hover:shadow-md transition-shadow">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-muted-foreground">
                {card.title}
              </CardTitle>
              <div className={`p-2 rounded-full ${card.bgColor}`}>
                <card.icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-foreground">
                {isLoading ? '...' : card.value}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                {card.description}
              </p>
              <Link href={card.href}>
                <Button variant="ghost" size="sm" className="mt-2 p-0 h-auto text-primary hover:text-primary/80">
                  Visualizza tutti →
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Quick Actions */}
      <div>
        <h2 className="text-xl font-poppins font-semibold text-foreground mb-4">
          Azioni Rapide
        </h2>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {quickActions.map((action) => (
            <Card key={action.title} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full text-white ${action.color}`}>
                    <action.icon className="h-5 w-5" />
                  </div>
                  <div>
                    <CardTitle className="text-base font-medium">
                      {action.title}
                    </CardTitle>
                    <CardDescription className="text-sm">
                      {action.description}
                    </CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                <Link href={action.href}>
                  <Button className={`w-full ${action.color} text-white`}>
                    <Plus className="h-4 w-4 mr-2" />
                    Aggiungi
                  </Button>
                </Link>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Welcome Message */}
      <Card className="bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
        <CardHeader>
          <CardTitle className="text-lg font-poppins">
            Benvenuto nel Sistema di Gestione Scolastica
          </CardTitle>
          <CardDescription>
            Utilizza questo sistema per gestire in modo efficiente scuole, aule e insegnanti. 
            Inizia creando una scuola, poi aggiungi le relative aule e insegnanti.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Link href="/dashboard/schools">
              <Button variant="outline" size="sm">
                Gestisci Scuole
              </Button>
            </Link>
            <Link href="/dashboard/classrooms">
              <Button variant="outline" size="sm">
                Gestisci Aule
              </Button>
            </Link>
            <Link href="/dashboard/teachers">
              <Button variant="outline" size="sm">
                Gestisci Insegnanti
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
