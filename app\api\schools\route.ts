import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { schoolSchema } from '@/lib/validations';
import { requireAuth } from '@/lib/auth';

// GET /api/schools - List all schools
export async function GET() {
  try {
    await requireAuth();
    
    const schools = await db.getSchools();
    return NextResponse.json(schools);
    
  } catch (error) {
    console.error('Error fetching schools:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    );
  }
}

// POST /api/schools - Create a new school
export async function POST(request: NextRequest) {
  try {
    await requireAuth();
    
    const body = await request.json();
    
    // Validate input
    const validatedData = schoolSchema.parse(body);
    
    // Create school
    const school = await db.createSchool(validatedData.name, validatedData.city);
    
    return NextResponse.json(school, { status: 201 });
    
  } catch (error) {
    console.error('Error creating school:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Dati non validi' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore durante la creazione della scuola' },
      { status: 500 }
    );
  }
}
