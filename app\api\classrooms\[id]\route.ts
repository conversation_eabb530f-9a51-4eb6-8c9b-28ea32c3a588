import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { classroomSchema } from '@/lib/validations';
import { requireAuth } from '@/lib/auth';

// GET /api/classrooms/[id] - Get a single classroom
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAuth();
    
    const { id } = await params;
    const classroomId = parseInt(id);
    
    if (isNaN(classroomId)) {
      return NextResponse.json(
        { error: 'ID aula non valido' },
        { status: 400 }
      );
    }
    
    const classroom = await db.getClassroomById(classroomId);
    
    if (!classroom) {
      return NextResponse.json(
        { error: 'Aula non trovata' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(classroom);
    
  } catch (error) {
    console.error('Error fetching classroom:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    );
  }
}

// PUT /api/classrooms/[id] - Update a classroom
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAuth();
    
    const { id } = await params;
    const classroomId = parseInt(id);
    
    if (isNaN(classroomId)) {
      return NextResponse.json(
        { error: 'ID aula non valido' },
        { status: 400 }
      );
    }
    
    const body = await request.json();
    
    // Validate input
    const validatedData = classroomSchema.parse(body);
    
    // Update classroom
    const classroom = await db.updateClassroom(
      classroomId,
      validatedData.room_number,
      validatedData.section,
      validatedData.school_id
    );
    
    if (!classroom) {
      return NextResponse.json(
        { error: 'Aula non trovata' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(classroom);
    
  } catch (error) {
    console.error('Error updating classroom:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Dati non validi' },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore durante l\'aggiornamento dell\'aula' },
      { status: 500 }
    );
  }
}

// DELETE /api/classrooms/[id] - Delete a classroom
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAuth();
    
    const { id } = await params;
    const classroomId = parseInt(id);
    
    if (isNaN(classroomId)) {
      return NextResponse.json(
        { error: 'ID aula non valido' },
        { status: 400 }
      );
    }
    
    const success = await db.deleteClassroom(classroomId);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Aula non trovata' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ message: 'Aula eliminata con successo' });
    
  } catch (error) {
    console.error('Error deleting classroom:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore durante l\'eliminazione dell\'aula' },
      { status: 500 }
    );
  }
}
