import { z } from 'zod';

// School validation schema
export const schoolSchema = z.object({
  name: z.string()
    .min(1, 'Il nome della scuola è obbligatorio')
    .max(255, 'Il nome della scuola non può superare i 255 caratteri'),
  city: z.string()
    .min(1, 'La città è obbligatoria')
    .max(255, 'La città non può superare i 255 caratteri'),
});

export type SchoolFormData = z.infer<typeof schoolSchema>;

// Classroom validation schema
export const classroomSchema = z.object({
  room_number: z.string()
    .min(1, 'Il numero dell\'aula è obbligatorio')
    .max(50, 'Il numero dell\'aula non può superare i 50 caratteri'),
  section: z.string()
    .min(1, 'La sezione è obbligatoria')
    .max(50, 'La sezione non può superare i 50 caratteri'),
  school_id: z.number()
    .min(1, 'Seleziona una scuola'),
});

export type ClassroomFormData = z.infer<typeof classroomSchema>;

// Teacher validation schema
export const teacherSchema = z.object({
  first_name: z.string()
    .min(1, 'Il nome è obbligatorio')
    .max(255, 'Il nome non può superare i 255 caratteri'),
  last_name: z.string()
    .min(1, 'Il cognome è obbligatorio')
    .max(255, 'Il cognome non può superare i 255 caratteri'),
  email: z.string()
    .email('Inserisci un indirizzo email valido')
    .max(255, 'L\'email non può superare i 255 caratteri'),
  phone: z.string()
    .max(50, 'Il telefono non può superare i 50 caratteri')
    .optional()
    .or(z.literal('')),
  subject: z.string()
    .min(1, 'La materia è obbligatoria')
    .max(255, 'La materia non può superare i 255 caratteri'),
  free_day: z.enum(['Lunedì', 'Martedì', 'Mercoledì', 'Giovedì', 'Venerdì', 'Sabato', 'Domenica'], {
    required_error: 'Seleziona un giorno libero',
  }),
});

export type TeacherFormData = z.infer<typeof teacherSchema>;

// Login validation schema
export const loginSchema = z.object({
  username: z.string()
    .min(1, 'Il nome utente è obbligatorio'),
  password: z.string()
    .min(1, 'La password è obbligatoria'),
});

export type LoginFormData = z.infer<typeof loginSchema>;

// Constants for form options
export const WEEKDAYS = [
  'Lunedì',
  'Martedì', 
  'Mercoledì',
  'Giovedì',
  'Venerdì',
  'Sabato',
  'Domenica'
] as const;

export const WEEKDAY_OPTIONS = WEEKDAYS.map(day => ({
  value: day,
  label: day
}));
