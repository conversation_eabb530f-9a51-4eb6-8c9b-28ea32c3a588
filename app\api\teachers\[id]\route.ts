import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { teacherSchema } from '@/lib/validations';
import { requireAuth } from '@/lib/auth';

// GET /api/teachers/[id] - Get a single teacher
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAuth();
    
    const { id } = await params;
    const teacherId = parseInt(id);
    
    if (isNaN(teacherId)) {
      return NextResponse.json(
        { error: 'ID insegnante non valido' },
        { status: 400 }
      );
    }
    
    const teacher = await db.getTeacherById(teacherId);
    
    if (!teacher) {
      return NextResponse.json(
        { error: 'Insegnante non trovato' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(teacher);
    
  } catch (error) {
    console.error('Error fetching teacher:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    );
  }
}

// PUT /api/teachers/[id] - Update a teacher
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAuth();
    
    const { id } = await params;
    const teacherId = parseInt(id);
    
    if (isNaN(teacherId)) {
      return NextResponse.json(
        { error: 'ID insegnante non valido' },
        { status: 400 }
      );
    }
    
    const body = await request.json();
    
    // Validate input
    const validatedData = teacherSchema.parse(body);
    
    // Update teacher
    const teacher = await db.updateTeacher(
      teacherId,
      validatedData.first_name,
      validatedData.last_name,
      validatedData.email,
      validatedData.phone || null,
      validatedData.subject,
      validatedData.free_day
    );
    
    if (!teacher) {
      return NextResponse.json(
        { error: 'Insegnante non trovato' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(teacher);
    
  } catch (error) {
    console.error('Error updating teacher:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Dati non validi' },
        { status: 400 }
      );
    }
    
    // Handle unique constraint violation for email
    if (error instanceof Error && error.message.includes('unique')) {
      return NextResponse.json(
        { error: 'Email già in uso da un altro insegnante' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore durante l\'aggiornamento dell\'insegnante' },
      { status: 500 }
    );
  }
}

// DELETE /api/teachers/[id] - Delete a teacher
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    await requireAuth();
    
    const { id } = await params;
    const teacherId = parseInt(id);
    
    if (isNaN(teacherId)) {
      return NextResponse.json(
        { error: 'ID insegnante non valido' },
        { status: 400 }
      );
    }
    
    const success = await db.deleteTeacher(teacherId);
    
    if (!success) {
      return NextResponse.json(
        { error: 'Insegnante non trovato' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ message: 'Insegnante eliminato con successo' });
    
  } catch (error) {
    console.error('Error deleting teacher:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore durante l\'eliminazione dell\'insegnante' },
      { status: 500 }
    );
  }
}
