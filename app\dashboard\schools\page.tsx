'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { schoolSchema, type SchoolFormData } from '@/lib/validations';
import { type School } from '@/lib/db';
import { Plus, Edit, Trash2, School as SchoolIcon } from 'lucide-react';

export default function SchoolsPage() {
  const [schools, setSchools] = useState<School[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSchool, setEditingSchool] = useState<School | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<SchoolFormData>({
    resolver: zodResolver(schoolSchema),
  });

  useEffect(() => {
    loadSchools();
  }, []);

  const loadSchools = async () => {
    try {
      const response = await fetch('/api/schools');
      if (response.ok) {
        const data = await response.json();
        setSchools(data);
      } else {
        setError('Errore nel caricamento delle scuole');
      }
    } catch (error) {
      setError('Errore di connessione');
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit = async (data: SchoolFormData) => {
    setIsSubmitting(true);
    setError('');

    try {
      const url = editingSchool ? `/api/schools/${editingSchool.id}` : '/api/schools';
      const method = editingSchool ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        await loadSchools();
        setIsDialogOpen(false);
        reset();
        setEditingSchool(null);
      } else {
        const result = await response.json();
        setError(result.error || 'Errore durante il salvataggio');
      }
    } catch (error) {
      setError('Errore di connessione');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = (school: School) => {
    setEditingSchool(school);
    reset({
      name: school.name,
      city: school.city,
    });
    setIsDialogOpen(true);
  };

  const handleDelete = async (school: School) => {
    if (!confirm(`Sei sicuro di voler eliminare la scuola "${school.name}"?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/schools/${school.id}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await loadSchools();
      } else {
        const result = await response.json();
        setError(result.error || 'Errore durante l\'eliminazione');
      }
    } catch (error) {
      setError('Errore di connessione');
    }
  };

  const handleNewSchool = () => {
    setEditingSchool(null);
    reset({ name: '', city: '' });
    setIsDialogOpen(true);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Caricamento scuole...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-poppins font-bold text-foreground">Gestione Scuole</h1>
          <p className="text-muted-foreground mt-2">
            Gestisci le scuole registrate nel sistema
          </p>
        </div>
        <Button onClick={handleNewSchool} className="bg-primary hover:bg-primary/90">
          <Plus className="h-4 w-4 mr-2" />
          Aggiungi Scuola
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 text-sm text-destructive bg-destructive/10 border border-destructive/20 rounded-md">
          {error}
        </div>
      )}

      {/* Schools Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <SchoolIcon className="h-5 w-5 mr-2" />
            Scuole ({schools.length})
          </CardTitle>
          <CardDescription>
            Elenco di tutte le scuole registrate nel sistema
          </CardDescription>
        </CardHeader>
        <CardContent>
          {schools.length === 0 ? (
            <div className="text-center py-8">
              <SchoolIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Nessuna scuola registrata</p>
              <Button onClick={handleNewSchool} variant="outline" className="mt-4">
                <Plus className="h-4 w-4 mr-2" />
                Aggiungi la prima scuola
              </Button>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nome Scuola</TableHead>
                  <TableHead>Città</TableHead>
                  <TableHead>Data Creazione</TableHead>
                  <TableHead className="text-right">Azioni</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {schools.map((school) => (
                  <TableRow key={school.id}>
                    <TableCell className="font-medium">{school.name}</TableCell>
                    <TableCell>{school.city}</TableCell>
                    <TableCell>
                      {new Date(school.created_at).toLocaleDateString('it-IT')}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(school)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDelete(school)}
                          className="text-destructive hover:text-destructive"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Dialog */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {editingSchool ? 'Modifica Scuola' : 'Aggiungi Nuova Scuola'}
            </DialogTitle>
            <DialogDescription>
              {editingSchool 
                ? 'Modifica i dettagli della scuola selezionata'
                : 'Inserisci i dettagli della nuova scuola'
              }
            </DialogDescription>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Nome Scuola</Label>
              <Input
                id="name"
                placeholder="Inserisci il nome della scuola"
                {...register('name')}
              />
              {errors.name && (
                <p className="text-sm text-destructive">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="city">Città</Label>
              <Input
                id="city"
                placeholder="Inserisci la città"
                {...register('city')}
              />
              {errors.city && (
                <p className="text-sm text-destructive">{errors.city.message}</p>
              )}
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsDialogOpen(false)}
              >
                Annulla
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-primary hover:bg-primary/90"
              >
                {isSubmitting ? 'Salvataggio...' : (editingSchool ? 'Aggiorna' : 'Salva')}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
