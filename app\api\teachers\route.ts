import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { teacherSchema } from '@/lib/validations';
import { requireAuth } from '@/lib/auth';

// GET /api/teachers - List all teachers
export async function GET() {
  try {
    await requireAuth();
    
    const teachers = await db.getTeachers();
    return NextResponse.json(teachers);
    
  } catch (error) {
    console.error('Error fetching teachers:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore interno del server' },
      { status: 500 }
    );
  }
}

// POST /api/teachers - Create a new teacher
export async function POST(request: NextRequest) {
  try {
    await requireAuth();
    
    const body = await request.json();
    
    // Validate input
    const validatedData = teacherSchema.parse(body);
    
    // Create teacher
    const teacher = await db.createTeacher(
      validatedData.first_name,
      validatedData.last_name,
      validatedData.email,
      validatedData.phone || null,
      validatedData.subject,
      validatedData.free_day
    );
    
    return NextResponse.json(teacher, { status: 201 });
    
  } catch (error) {
    console.error('Error creating teacher:', error);
    
    if (error instanceof Error && error.message === 'Authentication required') {
      return NextResponse.json(
        { error: 'Non autorizzato' },
        { status: 401 }
      );
    }
    
    if (error instanceof Error && error.name === 'ZodError') {
      return NextResponse.json(
        { error: 'Dati non validi' },
        { status: 400 }
      );
    }
    
    // Handle unique constraint violation for email
    if (error instanceof Error && error.message.includes('unique')) {
      return NextResponse.json(
        { error: 'Email già in uso da un altro insegnante' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Errore durante la creazione dell\'insegnante' },
      { status: 500 }
    );
  }
}
